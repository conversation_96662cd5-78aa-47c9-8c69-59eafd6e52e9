package me.zhyd.oauth.request;

import me.zhyd.oauth.cache.AuthStateCache;
import me.zhyd.oauth.config.AuthConfig;
import me.zhyd.oauth.config.AuthDefaultSource;

/**
 * 钉钉二维码登录
 *
 * <AUTHOR> (yadong.zhang0415(a)gmail.com)
 * @since 1.0.0
 */
public class AuthDingTalkRequest extends AbstractAuthDingtalkRequest {

    public AuthDingTalkRequest(AuthConfig config) {
        super(config, AuthDefaultSource.DINGTALK);
    }

    public AuthDingTalkRequest(AuthConfig config, AuthStateCache authStateCache) {
        super(config, AuthDefaultSource.DINGTALK, authStateCache);
    }
}
